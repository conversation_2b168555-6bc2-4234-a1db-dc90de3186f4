import asyncio
import json
import re
from typing import List, Dict, Any, Optional
from fastmcp import Client
from modelscope import AutoModelForCausalLM, AutoTokenizer

class QwenMCPClient:
    """基于Qwen3的MCP客户端，支持工具调用"""

    def __init__(self, model_path="./Qwen3-8B", mcp_server_url="http://127.0.0.1:4200/demo"):
        """
        初始化Qwen3模型和MCP客户端
        :param model_path: Qwen3模型路径
        :param mcp_server_url: MCP服务器URL
        """
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype="auto",
            device_map="auto"
        )
        self.mcp_server_url = mcp_server_url
        self.conversation_history = []

    async def query_mcp_tool(self, tool_name: str, params: dict):
        """
        调用MCP工具的统一入口
        :param tool_name: 工具名称
        :param params: 工具参数
        :return: 工具执行结果
        """
        async with <PERSON><PERSON>(self.mcp_server_url) as client:
            return await client.call_tool(tool_name, params)

    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用的MCP工具列表"""
        async with Client(self.mcp_server_url) as mcp_client:
            tools = await mcp_client.list_tools()
            return [{
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.inputSchema
            } for tool in tools]

    def generate_tool_prompt(self, tools: List[Dict[str, Any]]) -> str:
        """生成包含工具信息的系统提示"""
        tool_descriptions = []
        for tool in tools:
            params_desc = ""
            if "properties" in tool["parameters"]:
                params_list = []
                for param_name, param_info in tool["parameters"]["properties"].items():
                    param_type = param_info.get("type", "string")
                    param_desc = param_info.get("description", "")
                    params_list.append(f"{param_name} ({param_type}): {param_desc}")
                params_desc = ", ".join(params_list)

            tool_descriptions.append(f"- {tool['name']}: {tool['description']}. 参数: {params_desc}")

        return f"""你是一个智能助手，可以使用以下工具来帮助用户：

{chr(10).join(tool_descriptions)}

当你需要使用工具时，请按以下JSON格式回复：
```json
{{
    "tool_calls": [
        {{
            "name": "工具名称",
            "parameters": {{
                "参数名": "参数值"
            }}
        }}
    ]
}}
```

如果不需要使用工具，请直接回答用户的问题。"""

    def parse_tool_calls(self, response: str) -> Optional[List[Dict[str, Any]]]:
        """解析模型回复中的工具调用"""
        # 查找JSON代码块
        json_pattern = r'```json\s*(\{.*?\})\s*```'
        match = re.search(json_pattern, response, re.DOTALL)

        if match:
            try:
                json_str = match.group(1)
                parsed = json.loads(json_str)
                return parsed.get("tool_calls", [])
            except json.JSONDecodeError:
                return None
        return None

    def generate_response(self, messages: List[Dict[str, str]]) -> str:
        """使用Qwen3生成回复"""
        text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=True
        )

        inputs = self.tokenizer(text, return_tensors="pt")
        response_ids = self.model.generate(
            **inputs,
            max_new_tokens=2048,
            temperature=0.7,
            do_sample=True
        )[0][len(inputs.input_ids[0]):].tolist()

        response = self.tokenizer.decode(response_ids, skip_special_tokens=True)
        return response

async def chat_with_tools():
    """
    实现支持工具调用的聊天功能
    1. 初始化Qwen3模型和MCP客户端
    2. 获取可用工具列表
    3. 根据用户问题让模型决定是否调用工具
    4. 执行工具调用并整合结果生成最终回复
    """
    # 初始化Qwen MCP客户端
    client = QwenMCPClient()

    # 获取可用工具
    tools = await client.get_available_tools()
    tool_prompt = client.generate_tool_prompt(tools)

    # 用户提问示例
    user_query = "查询北京天气和贵州茅台股价"
    print(f"用户问题: {user_query}")

    # 构建包含工具信息的消息
    messages = [
        {"role": "system", "content": tool_prompt},
        {"role": "user", "content": user_query}
    ]

    # 第一次调用模型，让模型决定是否需要调用工具
    response = client.generate_response(messages)
    print(f"模型回复: {response}")

    # 解析工具调用
    tool_calls = client.parse_tool_calls(response)

    if tool_calls:
        print("检测到工具调用请求:")
        tool_results = []

        # 按顺序执行模型请求的所有工具
        for call in tool_calls:
            tool_name = call["name"]
            parameters = call["parameters"]
            print(f"正在执行 {tool_name}，参数: {parameters}")

            # 调用MCP工具并获取结果
            result = await client.query_mcp_tool(tool_name, parameters)
            print(f"工具返回: {result}")
            tool_results.append({
                "tool": tool_name,
                "parameters": parameters,
                "result": result
            })

        # 构建包含工具结果的消息，让模型生成最终回复
        tool_result_text = "\n".join([
            f"工具 {tr['tool']} 的执行结果: {tr['result']}"
            for tr in tool_results
        ])

        final_messages = [
            {"role": "system", "content": "请根据工具执行结果回答用户的问题。"},
            {"role": "user", "content": user_query},
            {"role": "assistant", "content": f"我已经调用了相关工具，结果如下：\n{tool_result_text}\n\n现在我来为您总结答案："}
        ]

        final_response = client.generate_response(final_messages)
        print(f"\n最终回复: {final_response}")
    else:
        # 如果模型认为不需要工具，直接返回模型回复
        print("直接回复:", response)

if __name__ == "__main__":
    # 运行异步聊天函数
    asyncio.run(chat_with_tools())