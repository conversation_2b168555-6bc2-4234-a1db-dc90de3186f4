from fastmcp import FastMCP
import os
import json
import math
import datetime
import requests
from typing import Dict, Any, List

from grpc_mcp_test import grpc_mcp_server

# 创建FastMCP应用实例，"demo"为应用名称
app = FastMCP("demo")

# ==================== 信息查询工具 ====================

# 注册天气查询工具，用于获取指定城市的天气信息
@app.tool(name="weather", description="城市天气查询")
def get_weather(city: str):
    """查询指定城市的天气信息"""
    # 预设的天气数据（实际应用中可替换为API调用）
    weather_data = {
        "北京": {"temp": 25, "condition": "晴", "humidity": "45%", "wind": "东北风2级"},
        "上海": {"temp": 28, "condition": "多云", "humidity": "60%", "wind": "东南风3级"},
        "广州": {"temp": 32, "condition": "雷阵雨", "humidity": "80%", "wind": "南风2级"},
        "深圳": {"temp": 30, "condition": "阴", "humidity": "75%", "wind": "东风1级"},
        "杭州": {"temp": 26, "condition": "小雨", "humidity": "85%", "wind": "东北风2级"}
    }
    # 返回对应城市的天气，不存在则返回错误信息
    return weather_data.get(city, {"error": f"未找到城市 {city} 的天气信息"})

# 注册股票查询工具，用于获取指定股票代码的价格信息
@app.tool(name="stock", description="股票价格查询")
def get_stock(code: str):
    """查询指定股票代码的价格信息"""
    # 预设的股票数据（实际应用中可替换为API调用）
    stock_data = {
        "600519": {"name": "贵州茅台", "price": 1825.0, "change": "+2.5%", "volume": "1.2万手"},
        "000858": {"name": "五粮液", "price": 158.3, "change": "-1.2%", "volume": "8.5万手"},
        "000001": {"name": "平安银行", "price": 12.45, "change": "+0.8%", "volume": "15.6万手"},
        "600036": {"name": "招商银行", "price": 35.67, "change": "+1.5%", "volume": "12.3万手"},
        "000002": {"name": "万科A", "price": 18.92, "change": "-0.5%", "volume": "9.8万手"}
    }
    # 返回对应股票的信息，不存在则返回错误信息
    return stock_data.get(code, {"error": f"未找到股票代码 {code} 的信息"})

# ==================== 计算工具 ====================

@app.tool(name="calculator", description="数学计算器，支持基本运算和数学函数")
def calculator(expression: str):
    """安全的数学表达式计算器"""
    try:
        # 允许的数学函数和常量
        allowed_names = {
            "abs": abs, "round": round, "min": min, "max": max,
            "sum": sum, "pow": pow, "sqrt": math.sqrt,
            "sin": math.sin, "cos": math.cos, "tan": math.tan,
            "log": math.log, "log10": math.log10, "exp": math.exp,
            "pi": math.pi, "e": math.e
        }

        # 安全评估表达式
        code = compile(expression, "<string>", "eval")
        for name in code.co_names:
            if name not in allowed_names:
                return {"error": f"不允许使用函数或变量: {name}"}

        result = eval(code, {"__builtins__": {}}, allowed_names)
        return {"expression": expression, "result": result}
    except Exception as e:
        return {"error": f"计算错误: {str(e)}"}

# ==================== 时间工具 ====================

@app.tool(name="current_time", description="获取当前时间信息")
def get_current_time(format: str = "default"):
    """获取当前时间，支持多种格式"""
    now = datetime.datetime.now()

    formats = {
        "default": now.strftime("%Y-%m-%d %H:%M:%S"),
        "date": now.strftime("%Y-%m-%d"),
        "time": now.strftime("%H:%M:%S"),
        "timestamp": int(now.timestamp()),
        "iso": now.isoformat(),
        "chinese": now.strftime("%Y年%m月%d日 %H时%M分%S秒"),
        "weekday": now.strftime("%A")
    }

    if format in formats:
        return {"current_time": formats[format], "format": format}
    else:
        return {
            "current_time": formats["default"],
            "available_formats": list(formats.keys())
        }

# ==================== 文件操作工具 ====================

@app.tool(name="read_file", description="读取文件内容")
def read_file(file_path: str, encoding: str = "utf-8"):
    """读取指定文件的内容"""
    try:
        if not os.path.exists(file_path):
            return {"error": f"文件不存在: {file_path}"}

        with open(file_path, 'r', encoding=encoding) as f:
            content = f.read()

        return {
            "file_path": file_path,
            "content": content,
            "size": len(content),
            "lines": len(content.split('\n'))
        }
    except Exception as e:
        return {"error": f"读取文件失败: {str(e)}"}

@app.tool(name="write_file", description="写入文件内容")
def write_file(file_path: str, content: str, encoding: str = "utf-8", mode: str = "w"):
    """写入内容到指定文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        with open(file_path, mode, encoding=encoding) as f:
            f.write(content)

        return {
            "file_path": file_path,
            "status": "success",
            "size": len(content),
            "mode": mode
        }
    except Exception as e:
        return {"error": f"写入文件失败: {str(e)}"}

@app.tool(name="list_files", description="列出目录中的文件")
def list_files(directory: str = ".", pattern: str = "*"):
    """列出指定目录中的文件"""
    try:
        if not os.path.exists(directory):
            return {"error": f"目录不存在: {directory}"}

        import glob
        search_pattern = os.path.join(directory, pattern)
        files = glob.glob(search_pattern)

        file_info = []
        for file_path in files:
            if os.path.isfile(file_path):
                stat = os.stat(file_path)
                file_info.append({
                    "name": os.path.basename(file_path),
                    "path": file_path,
                    "size": stat.st_size,
                    "modified": datetime.datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                })

        return {
            "directory": directory,
            "pattern": pattern,
            "files": file_info,
            "count": len(file_info)
        }
    except Exception as e:
        return {"error": f"列出文件失败: {str(e)}"}

# ==================== 网络工具 ====================

@app.tool(name="http_request", description="发送HTTP请求")
def http_request(url: str, method: str = "GET", headers: Dict[str, str] = None, data: str = None):
    """发送HTTP请求"""
    try:
        if headers is None:
            headers = {}

        response = requests.request(
            method=method.upper(),
            url=url,
            headers=headers,
            data=data,
            timeout=10
        )

        return {
            "url": url,
            "method": method,
            "status_code": response.status_code,
            "headers": dict(response.headers),
            "content": response.text[:1000],  # 限制内容长度
            "content_length": len(response.text)
        }
    except Exception as e:
        return {"error": f"HTTP请求失败: {str(e)}"}

# ==================== 文本处理工具 ====================

@app.tool(name="text_analysis", description="文本分析工具")
def text_analysis(text: str):
    """分析文本的基本统计信息"""
    try:
        lines = text.split('\n')
        words = text.split()
        characters = len(text)
        characters_no_spaces = len(text.replace(' ', ''))

        # 统计字符频率
        char_freq = {}
        for char in text.lower():
            if char.isalpha():
                char_freq[char] = char_freq.get(char, 0) + 1

        # 获取最常见的字符
        most_common_chars = sorted(char_freq.items(), key=lambda x: x[1], reverse=True)[:5]

        return {
            "text_length": characters,
            "characters_no_spaces": characters_no_spaces,
            "word_count": len(words),
            "line_count": len(lines),
            "paragraph_count": len([line for line in lines if line.strip()]),
            "most_common_chars": most_common_chars,
            "average_word_length": sum(len(word) for word in words) / len(words) if words else 0
        }
    except Exception as e:
        return {"error": f"文本分析失败: {str(e)}"}

@app.tool(name="json_formatter", description="JSON格式化工具")
def json_formatter(json_string: str, indent: int = 2):
    """格式化JSON字符串"""
    try:
        parsed = json.loads(json_string)
        formatted = json.dumps(parsed, indent=indent, ensure_ascii=False)
        return {
            "original": json_string,
            "formatted": formatted,
            "valid": True
        }
    except json.JSONDecodeError as e:
        return {
            "original": json_string,
            "error": f"JSON格式错误: {str(e)}",
            "valid": False
        }

# ==================== 系统信息工具 ====================

@app.tool(name="system_info", description="获取系统信息")
def get_system_info():
    """获取基本的系统信息"""
    try:
        import platform
        import psutil

        return {
            "platform": platform.platform(),
            "system": platform.system(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": f"{psutil.virtual_memory().total / (1024**3):.2f} GB",
            "memory_available": f"{psutil.virtual_memory().available / (1024**3):.2f} GB",
            "disk_usage": f"{psutil.disk_usage('/').percent}%"
        }
    except ImportError:
        return {
            "platform": "信息不可用 (需要安装 psutil)",
            "note": "运行 pip install psutil 以获取详细系统信息"
        }
    except Exception as e:
        return {"error": f"获取系统信息失败: {str(e)}"}
    
# @app.tool(name="get_image_info", description="当用户说需要获取图片的内容信息或查看当前画面是否有异常信息时调用")
# def get_image_info():
#     """当用户说需要获取图片的内容信息或查看当前画面是否有异常信息时调用"""
#     grpc_mcp_server.run()


#     pass

if __name__ == "__main__":
    print("启动MCP服务器...")
    print("可用工具:")
    print("- weather: 天气查询")
    print("- stock: 股票查询")
    print("- calculator: 数学计算")
    print("- current_time: 时间查询")
    print("- read_file: 读取文件")
    print("- write_file: 写入文件")
    print("- list_files: 列出文件")
    print("- http_request: HTTP请求")
    print("- text_analysis: 文本分析")
    print("- json_formatter: JSON格式化")
    print("- system_info: 系统信息")
    print("=" * 50)

    # 启动HTTP服务，支持流式响应
    app.run(
        transport="streamable-http",  # 使用支持流式传输的HTTP协议
        host="127.0.0.1",            # 监听本地地址
        port=4200,                   # 服务端口
        path="/demo",                # 服务路径前缀
        log_level="debug",           # 调试日志级别
    )