#!/usr/bin/env python3
"""
Qwen3 MCP 演示启动脚本
自动启动服务端和客户端进行演示
"""

import asyncio
import subprocess
import time
import sys
import os
from threading import Thread

def start_server():
    """启动MCP服务端"""
    print("🚀 启动MCP服务端...")
    try:
        subprocess.run([sys.executable, "test_mcp_server.py"], check=True)
    except KeyboardInterrupt:
        print("📴 MCP服务端已停止")
    except Exception as e:
        print(f"❌ 服务端启动失败: {e}")

def wait_for_server(max_wait=30):
    """等待服务端启动"""
    import requests
    
    print("⏳ 等待MCP服务端启动...")
    for i in range(max_wait):
        try:
            response = requests.get("http://127.0.0.1:4200/demo", timeout=1)
            if response.status_code == 200:
                print("✅ MCP服务端已就绪")
                return True
        except:
            pass
        time.sleep(1)
        print(f"   等待中... ({i+1}/{max_wait})")
    
    print("❌ 服务端启动超时")
    return False

async def run_demo():
    """运行演示"""
    print("🎯 开始Qwen3 MCP演示")
    print("=" * 50)
    
    # 检查模型文件
    if not os.path.exists("./Qwen3-8B"):
        print("❌ 未找到Qwen3-8B模型文件")
        print("请确保模型文件在当前目录的Qwen3-8B文件夹中")
        return
    
    # 启动服务端（后台线程）
    server_thread = Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # 等待服务端就绪
    if not wait_for_server():
        return
    
    # 导入并运行客户端
    try:
        from qwen3_mcp_client import QwenMCPClient
        
        print("🤖 初始化Qwen3 MCP客户端...")
        client = QwenMCPClient()
        
        # 运行几个演示查询
        demo_queries = [
            "查询北京天气",
            "计算 2^10 + sqrt(144)",
            "现在几点了？",
            "分析这段文本：'Hello World! This is a test message with 123 numbers.'"
        ]
        
        print("\n🎪 开始演示查询...")
        print("=" * 50)
        
        for i, query in enumerate(demo_queries, 1):
            print(f"\n📝 演示 {i}: {query}")
            print("-" * 30)
            
            try:
                result = await client.chat_with_tools(query)
                print(f"🤖 回答: {result}")
            except Exception as e:
                print(f"❌ 查询失败: {e}")
            
            print("\n" + "="*50)
            
            # 等待一下再进行下一个查询
            await asyncio.sleep(2)
        
        print("\n🎉 演示完成！")
        print("\n💡 您可以运行以下命令进入交互模式：")
        print("   python qwen3_mcp_client.py")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保已安装所需依赖：pip install modelscope torch transformers fastmcp")
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def main():
    """主函数"""
    print("🌟 Qwen3 MCP 智能助手演示")
    print("=" * 50)
    
    try:
        asyncio.run(run_demo())
    except KeyboardInterrupt:
        print("\n👋 演示已中断")
    except Exception as e:
        print(f"❌ 演示出错: {e}")
    
    print("\n📋 演示结束")

if __name__ == "__main__":
    main()
