#!/usr/bin/env python3
"""
Qwen3 MCP 环境设置脚本
自动安装依赖并检查环境
"""

import subprocess
import sys
import os
import importlib

def run_command(command, description):
    """运行命令并显示进度"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def install_dependencies():
    """安装依赖包"""
    print("📦 开始安装依赖包...")
    
    # 基础依赖
    dependencies = [
        "torch",
        "transformers", 
        "modelscope",
        "fastmcp",
        "requests",
        "psutil"
    ]
    
    # 检查已安装的包
    print("\n🔍 检查当前环境...")
    installed = []
    missing = []
    
    for dep in dependencies:
        if check_package(dep):
            installed.append(dep)
        else:
            missing.append(dep)
    
    if not missing:
        print("🎉 所有依赖都已安装！")
        return True
    
    print(f"\n📋 需要安装的包: {', '.join(missing)}")
    
    # 安装缺失的包
    success = True
    for package in missing:
        if package == "torch":
            # PyTorch需要特殊处理
            cmd = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
            if not run_command(cmd, f"安装 {package}"):
                success = False
        else:
            cmd = f"pip install {package}"
            if not run_command(cmd, f"安装 {package}"):
                success = False
    
    return success

def check_model_files():
    """检查模型文件"""
    print("\n🔍 检查Qwen3模型文件...")
    
    model_path = "./Qwen3-8B"
    if os.path.exists(model_path):
        # 检查关键文件
        required_files = [
            "config.json",
            "tokenizer.json", 
            "tokenizer_config.json"
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(os.path.join(model_path, file)):
                missing_files.append(file)
        
        if missing_files:
            print(f"⚠️  模型文件不完整，缺少: {', '.join(missing_files)}")
            return False
        else:
            print("✅ Qwen3模型文件完整")
            return True
    else:
        print("❌ 未找到Qwen3-8B模型文件夹")
        print("请确保模型文件在当前目录的Qwen3-8B文件夹中")
        return False

def create_test_script():
    """创建测试脚本"""
    test_script = """#!/usr/bin/env python3
import sys
try:
    from modelscope import AutoTokenizer
    from fastmcp import FastMCP
    import torch
    print("✅ 所有依赖导入成功！")
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print("🎉 环境设置完成，可以开始使用了！")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)
"""
    
    with open("test_env.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("📝 已创建环境测试脚本: test_env.py")

def main():
    """主函数"""
    print("🌟 Qwen3 MCP 环境设置")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，请手动安装")
        sys.exit(1)
    
    # 检查模型文件
    model_ok = check_model_files()
    
    # 创建测试脚本
    create_test_script()
    
    print("\n" + "=" * 50)
    print("🎯 设置完成！")
    
    if model_ok:
        print("\n📋 下一步操作：")
        print("1. 运行测试: python test_env.py")
        print("2. 启动演示: python start_demo.py")
        print("3. 交互模式: python qwen3_mcp_client.py")
    else:
        print("\n⚠️  请先下载Qwen3-8B模型文件到当前目录")
        print("模型下载地址: https://modelscope.cn/models/qwen/Qwen3-8B")
    
    print("\n📚 查看详细说明: README.md")

if __name__ == "__main__":
    main()
