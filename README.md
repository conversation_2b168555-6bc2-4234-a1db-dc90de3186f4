# Qwen3 MCP 智能助手

基于Qwen3大语言模型和MCP（Model Context Protocol）的智能助手系统，支持工具调用和多种实用功能。

## 项目结构

```
├── qwen3_test.py              # 原始Qwen3测试代码
├── test_mcp_vllm_client.py    # 修改后的客户端（基于Qwen3）
├── qwen3_mcp_client.py        # 新的Qwen3 MCP客户端
├── test_mcp_server.py         # 扩展的MCP服务端
├── Qwen3-8B/                  # Qwen3模型文件
└── README.md                  # 使用说明
```

## 功能特性

### MCP服务端工具

1. **信息查询工具**
   - `weather`: 城市天气查询
   - `stock`: 股票价格查询

2. **计算工具**
   - `calculator`: 数学计算器，支持基本运算和数学函数

3. **时间工具**
   - `current_time`: 获取当前时间信息（支持多种格式）

4. **文件操作工具**
   - `read_file`: 读取文件内容
   - `write_file`: 写入文件内容
   - `list_files`: 列出目录中的文件

5. **网络工具**
   - `http_request`: 发送HTTP请求

6. **文本处理工具**
   - `text_analysis`: 文本分析（字数统计、字符频率等）
   - `json_formatter`: JSON格式化工具

7. **系统信息工具**
   - `system_info`: 获取系统信息

### 客户端特性

- 基于本地部署的Qwen3-8B模型
- 自动识别用户需求并调用相应工具
- 支持交互式聊天模式
- 智能工具调用和结果整合

## 安装依赖

```bash
# 安装基础依赖
pip install modelscope torch transformers
pip install fastmcp requests psutil

# 如果需要GPU支持
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 使用方法

### 1. 启动MCP服务端

```bash
python test_mcp_server.py
```

服务端将在 `http://127.0.0.1:4200/demo` 启动，提供以下工具：

- 天气查询、股票查询
- 数学计算、时间查询
- 文件操作、网络请求
- 文本分析、JSON格式化
- 系统信息查询

### 2. 运行客户端

#### 方式一：使用新的客户端（推荐）

```bash
python qwen3_mcp_client.py
```

#### 方式二：使用修改后的原客户端

```bash
python test_mcp_vllm_client.py
```

### 3. 交互示例

```
用户: 查询北京天气和贵州茅台股价

助手: 我来为您查询北京的天气和贵州茅台的股价信息。

[工具调用]
- 正在执行 weather，参数: {"city": "北京"}
- 工具返回: {"temp": 25, "condition": "晴", "humidity": "45%", "wind": "东北风2级"}

- 正在执行 stock，参数: {"code": "600519"}  
- 工具返回: {"name": "贵州茅台", "price": 1825.0, "change": "+2.5%", "volume": "1.2万手"}

根据查询结果：

**北京天气**：
- 温度：25°C
- 天气：晴
- 湿度：45%
- 风力：东北风2级

**贵州茅台股价**：
- 股票名称：贵州茅台
- 当前价格：1825.0元
- 涨跌幅：+2.5%
- 成交量：1.2万手

今天北京天气不错，适合出行。贵州茅台股价表现良好，上涨2.5%。
```

## 工具使用示例

### 计算工具
```
用户: 计算 sqrt(16) + 2^3
助手: [调用calculator工具] 结果是12.0
```

### 文件操作
```
用户: 读取当前目录下的README.md文件
助手: [调用read_file工具] 显示文件内容
```

### 时间查询
```
用户: 现在几点了？
助手: [调用current_time工具] 当前时间是2024-01-15 14:30:25
```

## 自定义工具

您可以在 `test_mcp_server.py` 中添加更多工具：

```python
@app.tool(name="your_tool", description="工具描述")
def your_function(param1: str, param2: int = 0):
    """您的工具函数"""
    try:
        # 工具逻辑
        result = f"处理结果: {param1} - {param2}"
        return {"result": result, "status": "success"}
    except Exception as e:
        return {"error": f"处理失败: {str(e)}"}
```

## 注意事项

1. **模型路径**：确保 `Qwen3-8B` 模型文件在正确位置
2. **内存需求**：Qwen3-8B模型需要较大内存，建议16GB以上
3. **GPU支持**：如有GPU可显著提升推理速度
4. **网络工具**：使用网络相关工具时注意安全性
5. **文件操作**：文件操作工具有安全风险，请谨慎使用

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型路径是否正确
   - 确认有足够的内存和存储空间

2. **MCP连接失败**
   - 确认服务端已启动
   - 检查端口4200是否被占用

3. **工具调用失败**
   - 查看服务端日志
   - 确认工具参数格式正确

## 扩展开发

系统采用模块化设计，易于扩展：

- **添加新工具**：在服务端注册新的工具函数
- **改进模型**：替换或微调Qwen3模型
- **优化性能**：调整模型参数和推理配置
- **增强安全**：添加权限控制和输入验证

## 许可证

本项目仅供学习和研究使用。
