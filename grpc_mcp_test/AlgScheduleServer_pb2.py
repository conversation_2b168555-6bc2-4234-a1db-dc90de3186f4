# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: AlgScheduleServer.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'AlgScheduleServer.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x41lgScheduleServer.proto\x12\x11\x41lgScheduleServer\"!\n\tPointInfo\x12\t\n\x01x\x18\x01 \x01(\x05\x12\t\n\x01y\x18\x02 \x01(\x05\"p\n\x08\x41reaInfo\x12/\n\tpointList\x18\x01 \x03(\x0b\x32\x1c.AlgScheduleServer.PointInfo\x12\r\n\x05state\x18\x02 \x01(\x05\x12\x11\n\tframeType\x18\x03 \x01(\t\x12\x11\n\tthreshold\x18\x04 \x01(\x01\"B\n\rMarkPointInfo\x12\t\n\x01x\x18\x01 \x01(\x05\x12\t\n\x01y\x18\x02 \x01(\x05\x12\r\n\x05value\x18\x03 \x01(\x01\x12\x0c\n\x04type\x18\x04 \x01(\x05\"h\n\x08MarkInfo\x12*\n\x04\x61rea\x18\x01 \x03(\x0b\x32\x1c.AlgScheduleServer.PointInfo\x12\x30\n\x06points\x18\x02 \x03(\x0b\x32 .AlgScheduleServer.MarkPointInfo\"U\n\nMateRegion\x12/\n\tpointList\x18\x01 \x03(\x0b\x32\x1c.AlgScheduleServer.PointInfo\x12\x16\n\x0emateRegionCode\x18\x02 \x01(\t\"3\n\x06SubAlg\x12\x0f\n\x07\x61lgName\x18\x01 \x01(\t\x12\n\n\x02ip\x18\x02 \x01(\t\x12\x0c\n\x04port\x18\x03 \x01(\x05\"\xaa\x04\n\x13ObjectDetectRequest\x12\x0e\n\x06taskId\x18\x01 \x01(\t\x12\x10\n\x08\x63\x61meraId\x18\x02 \x01(\t\x12\x0f\n\x07\x61lgName\x18\x03 \x01(\t\x12-\n\x08\x61reaList\x18\x04 \x03(\x0b\x32\x1b.AlgScheduleServer.AreaInfo\x12-\n\x08markList\x18\x05 \x03(\x0b\x32\x1b.AlgScheduleServer.MarkInfo\x12\r\n\x05image\x18\x06 \x03(\x0c\x12\x14\n\x0ctemplateFile\x18\x07 \x03(\x0c\x12\x10\n\x08presetId\x18\x08 \x01(\x05\x12\x1b\n\x13\x65nableDeduplication\x18\t \x01(\x05\x12-\n\nsubAlgList\x18\n \x03(\x0b\x32\x19.AlgScheduleServer.SubAlg\x12\x10\n\x08taskType\x18\x0b \x01(\x05\x12\x35\n\x0emateRegionList\x18\x0c \x03(\x0b\x32\x1d.AlgScheduleServer.MateRegion\x12\x0c\n\x04\x63onf\x18\r \x01(\x01\x12\x16\n\x0ewidthThreshold\x18\x0e \x01(\x05\x12\x17\n\x0fheightThreshold\x18\x0f \x01(\x05\x12\x46\n\x08metadata\x18\x10 \x03(\x0b\x32\x34.AlgScheduleServer.ObjectDetectRequest.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x86\x01\n\nEventPoint\x12\x0e\n\x06result\x18\x01 \x01(\t\x12\r\n\x05score\x18\x02 \x01(\x01\x12\r\n\x05value\x18\x03 \x01(\x01\x12\x32\n\x0cobjectRegion\x18\x04 \x03(\x0b\x32\x1c.AlgScheduleServer.PointInfo\x12\x16\n\x0emateRegionCode\x18\x05 \x01(\t\"\xcb\x01\n\x11ObjectDetectReply\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0e\n\x06taskId\x18\x03 \x01(\t\x12\x35\n\x0e\x65ventPointList\x18\x04 \x03(\x0b\x32\x1d.AlgScheduleServer.EventPoint\x12\x12\n\nimageIndex\x18\x05 \x01(\x05\x12\x13\n\x0b\x64\x65tectAgain\x18\x06 \x01(\x05\x12\x12\n\nimageWidth\x18\x07 \x01(\x05\x12\x13\n\x0bimageHeight\x18\x08 \x01(\x05\"&\n\x08TimeInfo\x12\r\n\x05start\x18\x01 \x01(\x01\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x01\"\xb4\x03\n\x1bObjectDetectByStreamRequest\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x05\x12\x10\n\x08\x63\x61meraId\x18\x02 \x01(\t\x12\x0f\n\x07rtspUrl\x18\x03 \x01(\t\x12\x31\n\x0cTimeInfoList\x18\x04 \x03(\x0b\x32\x1b.AlgScheduleServer.TimeInfo\x12\x17\n\x0fminObjectHeight\x18\x05 \x01(\x05\x12\x16\n\x0eminObjectWidth\x18\x06 \x01(\x05\x12\x11\n\tconfThres\x18\x07 \x01(\x01\x12\x13\n\x0breportLabel\x18\x08 \x03(\t\x12\x12\n\nrepeatTime\x18\t \x01(\x05\x12-\n\x08\x61reaList\x18\n \x03(\x0b\x32\x1b.AlgScheduleServer.AreaInfo\x12\x12\n\nfliterType\x18\x0b \x03(\t\x12N\n\x08metadata\x18\x0c \x03(\x0b\x32<.AlgScheduleServer.ObjectDetectByStreamRequest.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\":\n\x19ObjectDetectByStreamReply\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\"M\n\x12UpdateModelRequest\x12\x10\n\x08\x66ileName\x18\x01 \x01(\t\x12\x10\n\x08\x66ileType\x18\x02 \x01(\t\x12\x13\n\x0b\x64ownloadUrl\x18\x03 \x01(\t\"1\n\x10UpdateModelReply\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\"]\n\x0b\x42oundingBox\x12/\n\tpointList\x18\x01 \x03(\x0b\x32\x1c.AlgScheduleServer.PointInfo\x12\x0e\n\x06result\x18\x02 \x01(\t\x12\r\n\x05score\x18\x03 \x01(\x01\"\xfb\x01\n\x19\x46\x61lseAlarmSuppressRequest\x12\x10\n\x08\x63\x61meraId\x18\x01 \x01(\t\x12\x0f\n\x07\x61lgName\x18\x02 \x01(\t\x12\r\n\x05image\x18\x03 \x01(\x0c\x12-\n\x05\x62oxes\x18\x04 \x03(\x0b\x32\x1e.AlgScheduleServer.BoundingBox\x12L\n\x08metadata\x18\x05 \x03(\x0b\x32:.AlgScheduleServer.FalseAlarmSuppressRequest.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"8\n\x17\x46\x61lseAlarmSuppressReply\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t2\xb0\x03\n\x07Greeter\x12^\n\x0cObjectDetect\x12&.AlgScheduleServer.ObjectDetectRequest\x1a$.AlgScheduleServer.ObjectDetectReply\"\x00\x12v\n\x14ObjectDetectByStream\x12..AlgScheduleServer.ObjectDetectByStreamRequest\x1a,.AlgScheduleServer.ObjectDetectByStreamReply\"\x00\x12[\n\x0bUpdateModel\x12%.AlgScheduleServer.UpdateModelRequest\x1a#.AlgScheduleServer.UpdateModelReply\"\x00\x12p\n\x12\x46\x61lseAlarmSuppress\x12,.AlgScheduleServer.FalseAlarmSuppressRequest\x1a*.AlgScheduleServer.FalseAlarmSuppressReply\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'AlgScheduleServer_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_OBJECTDETECTREQUEST_METADATAENTRY']._loaded_options = None
  _globals['_OBJECTDETECTREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_OBJECTDETECTBYSTREAMREQUEST_METADATAENTRY']._loaded_options = None
  _globals['_OBJECTDETECTBYSTREAMREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_FALSEALARMSUPPRESSREQUEST_METADATAENTRY']._loaded_options = None
  _globals['_FALSEALARMSUPPRESSREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_POINTINFO']._serialized_start=46
  _globals['_POINTINFO']._serialized_end=79
  _globals['_AREAINFO']._serialized_start=81
  _globals['_AREAINFO']._serialized_end=193
  _globals['_MARKPOINTINFO']._serialized_start=195
  _globals['_MARKPOINTINFO']._serialized_end=261
  _globals['_MARKINFO']._serialized_start=263
  _globals['_MARKINFO']._serialized_end=367
  _globals['_MATEREGION']._serialized_start=369
  _globals['_MATEREGION']._serialized_end=454
  _globals['_SUBALG']._serialized_start=456
  _globals['_SUBALG']._serialized_end=507
  _globals['_OBJECTDETECTREQUEST']._serialized_start=510
  _globals['_OBJECTDETECTREQUEST']._serialized_end=1064
  _globals['_OBJECTDETECTREQUEST_METADATAENTRY']._serialized_start=1017
  _globals['_OBJECTDETECTREQUEST_METADATAENTRY']._serialized_end=1064
  _globals['_EVENTPOINT']._serialized_start=1067
  _globals['_EVENTPOINT']._serialized_end=1201
  _globals['_OBJECTDETECTREPLY']._serialized_start=1204
  _globals['_OBJECTDETECTREPLY']._serialized_end=1407
  _globals['_TIMEINFO']._serialized_start=1409
  _globals['_TIMEINFO']._serialized_end=1447
  _globals['_OBJECTDETECTBYSTREAMREQUEST']._serialized_start=1450
  _globals['_OBJECTDETECTBYSTREAMREQUEST']._serialized_end=1886
  _globals['_OBJECTDETECTBYSTREAMREQUEST_METADATAENTRY']._serialized_start=1017
  _globals['_OBJECTDETECTBYSTREAMREQUEST_METADATAENTRY']._serialized_end=1064
  _globals['_OBJECTDETECTBYSTREAMREPLY']._serialized_start=1888
  _globals['_OBJECTDETECTBYSTREAMREPLY']._serialized_end=1946
  _globals['_UPDATEMODELREQUEST']._serialized_start=1948
  _globals['_UPDATEMODELREQUEST']._serialized_end=2025
  _globals['_UPDATEMODELREPLY']._serialized_start=2027
  _globals['_UPDATEMODELREPLY']._serialized_end=2076
  _globals['_BOUNDINGBOX']._serialized_start=2078
  _globals['_BOUNDINGBOX']._serialized_end=2171
  _globals['_FALSEALARMSUPPRESSREQUEST']._serialized_start=2174
  _globals['_FALSEALARMSUPPRESSREQUEST']._serialized_end=2425
  _globals['_FALSEALARMSUPPRESSREQUEST_METADATAENTRY']._serialized_start=1017
  _globals['_FALSEALARMSUPPRESSREQUEST_METADATAENTRY']._serialized_end=1064
  _globals['_FALSEALARMSUPPRESSREPLY']._serialized_start=2427
  _globals['_FALSEALARMSUPPRESSREPLY']._serialized_end=2483
  _globals['_GREETER']._serialized_start=2486
  _globals['_GREETER']._serialized_end=2918
# @@protoc_insertion_point(module_scope)
